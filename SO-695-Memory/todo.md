# Epic: feat-SO-695 - Sponsor Cockpit Improvements

---

## Task: SO-714 - Mudanças no Status do Consolidado e Filtros

### Mudanças Visuais no SponsorCockpitSummary

#### Alterações nos Cards de Status

1.  [x] Alterar "Desembolsado" para "Em Análise"
    *   [x] Trocar texto do card
    *   [x] Atualizar ícone para "dots-horizontal-circle-outline"
    *   [x] Remover funcionalidade de filtro e ícone de filtro

2.  [x] Alterar "Formalização" para "Aguardando Formalização"
    *   [x] Trocar texto do card
    *   [x] Atualizar ícone para "dots-horizontal-circle-outline"
    *   [x] Remover funcionalidade de filtro e ícone de filtro

3.  [x] Alterar "Aguardando Desembolso" para "Em Formalização"
    *   [x] Trocar texto do card
    *   [x] Atualizar ícone para "clipboard-text-outline"
    *   [x] Remover funcionalidade de filtro e ícone de filtro

4.  [x] Alterar "Cancelado" para "Pronto para Desembolso"
    *   [x] Trocar texto do card
    *   [x] Atualizar ícone para "send-outline"
    *   [x] Remover funcionalidade de filtro e ícone de filtro

#### Remoção de Funcionalidades de Filtro

1.  [x] Remover eventos de filtro do componente SponsorCockpitSummary
    *   [x] Remover @filterByAwaitingDisbursement
    *   [x] Remover @filterByDisbursed
    *   [x] Remover @filterByCanceled
    *   [x] Remover @filterByFormalization

2.  [x] Remover botões de filtro e seus respectivos ícones
    *   [x] Remover <farm-btn icon> de todos os cards
    *   [x] Remover <farm-icon size="md">filter-outline</farm-icon>

3.  [x] Limpar métodos relacionados aos filtros no componente pai (SponsorCockpitList)
    *   [x] Remover métodos filterBy
    *   [x] Remover listeners de eventos de filtro

#### Refatoração de Layout (Quality of Life)

1.  [x] Refatorar SponsorCockpitSummary para usar `farm-row` e `farm-col`
    *   [x] Remover arquivo `SponsorCockpitSummary.scss`
    *   [x] Substituir wrapper `div` por `farm-row`
    *   [x] Envolver cada `farm-card` com `farm-col` e aplicar props responsivas (`cols`, `md`, `lg`)
    *   [x] Remover classes CSS customizadas (`sponsor-cockpit-summary`, `sponsor-cockpit-summary__item`) do template Vue

### Adição do Filtro de Tipo de Importação

#### Implementação Inicial (Mock)

1.  [x] Adicionar novo farm-select após o filtro de status
    *   [x] Adicionar label "Tipo de Importação"
    *   [x] Criar array com valores mockados:

        ```typescript
        [
            { id: 1, desc: "API" },
            { id: 2, desc: "Importação" },
            { id: 3, desc: "Criado por cliente" }
        ]
        ```
    *   [x] Implementar v-model para o novo filtro
    *   [x] Adicionar o novo filtro ao objeto filters

2.  [x] Ajustar a lógica de filtragem
    *   [x] Atualizar método handleApplyFilters para incluir o novo filtro
    *   [x] Atualizar método handleCleanFilters para limpar o novo filtro

### Adição do Botão Reprocessar (Apenas Botão UI)

1.  [x] Adicionar novo botão ao lado do botão Exportar
    *   [x] Implementar <farm-btn plain> com texto "Reprocessar"
    *   [x] Posicionar à esquerda do botão Exportar
    *   [x] Adicionar método `@click` inicial (`onReprocess`) para abrir o modal (implementação do modal em SO-715)
    *   [x] **[NOVO] Controlar exibição do botão via permissão:**
        *   [x] Criado composable `useSponsorCockpitPermissions` que lê as roles do usuário diretamente do Vuex (`userAccess/currentUserRoles`).
        *   [x] O botão só aparece se a role `meusclientes.originacoes_sacados` for igual a 2 (perfil com permissão de edição).
        *   [x] Removida chamada duplicada de busca de roles, centralizando a lógica de permissão.
        *   [x] Padrão de tipagem dos composables ajustado para usar `ComputedRef` importado diretamente de `vue`.

### Adição de Ícones para Tipo de Importação na Tabela

#### Modificações em SponsorCockpitListTable

1.  [x] Identificar template para a coluna "ID DA OPERAÇÃO" no SponsorCockpitListTable
    *   [x] Verificar se já existe um template para essa coluna ou criar um novo

2.  [x] Utilizar o novo componente MaterialIcon para os diferentes tipos de importação
    *   [x] Definir ícones do Material Design para cada tipo de importação:
        *   [x] API (id: 2) - "cable"
        *   [x] Importação (id: 3) - "publish"
        *   [x] Criado por cliente (id: 1) - "person"

3.  [x] Modificar o template da coluna para exibir o ícone correspondente
    *   [x] Adicionar lógica condicional para mostrar o ícone baseado no tipo de importação
    *   [x] Implementar o componente MaterialIcon:

        ```vue
        <material-icon
            type="outlined"
            size="sm"
            color="primary">
            {{ getRandomIcon() }} <!-- Temporário: será substituído pela função real após backend -->
        </material-icon>
        ```
    *   [x] Posicionar o ícone ao lado do ID da operação
    *   [x] Estilizar o ícone conforme necessário

#### Ajustes no Mock de Dados

1.  [x] Modificar os dados mockados da tabela para incluir o tipo de importação
    *   [x] ~~Adicionar campo "importType" aos itens da tabela com valores 1, 2 ou 3~~
        *   Implementação provisória: adicionado enum com constantes e função para randomizar o tipo de importação
    *   [x] Documentar que esses valores devem ser substituídos quando o backend estiver pronto

### Melhorias na Tabela de Cockpit

1.  [x] Atualizar Textos dos Cabeçalhos da Tabela (`headers.ts`)
    *   [x] Alterar 'Nome' para 'Nome do Sacado'
    *   [x] Remover 'Última Atualização'
    *   [x] Alterar 'Vencimento' para 'Vencimento da Operação'
    *   [x] Adicionar 'Total pago ao fornecedor'

2.  [x] Corrigir Alinhamento do Ícone de Ordenação no Cabeçalho (`SponsorCockpitListTable.scss`)
    *   [x] Aplicar `display: inline-flex` e `align-items: center` ao `.header-text`
    *   [x] Remover `position: absolute` do `.farm-icon` dentro do header
    *   [x] Mover estilos de `SponsorCockpitListTable.vue` para `SponsorCockpitListTable.scss`

3.  [x] Atualizar Constantes de Status
    *   [x] Adicionar novos status:
        *   [x] UNDER_ANALYSIS (1)
        *   [x] READY_FOR_DISBURSEMENT (2)
        *   [x] WAITING_FORMALIZATION (6)
    *   [x] Atualizar textos dos status:
        *   [x] "Em Análise" para UNDER_ANALYSIS
        *   [x] "Aguardando Desembolso" para READY_FOR_DISBURSEMENT
        *   [x] "Formalização" para CANCELED
        *   [x] "Cancelado" para WAITING_FORMALIZATION

4.  [x] Atualizar Estrutura da Tabela
    *   [x] Renomear campo `totalPaid` para `requestedValue` nos headers
    *   [x] Ajustar alinhamento da coluna `total` para `left`
    *   [x] Remover campo `updatedAt` e substituir por `requestedValue`
    *   [x] Atualizar builder para incluir `requestedValue` ao invés de `updatedAt`

### Próximos Passos (Aguardando Backend - SO-714)

*   [ ] Atualizar integrações quando o backend estiver pronto (Filtros, Status, Ícones Tabela)
*   [ ] Ajustar tipos e interfaces conforme novas respostas da API
*   [ ] Atualizar testes relacionados às novas mudanças
*   [ ] Substituir dados mockados do Tipo de Importação pelos dados reais da API
    *   [ ] **Substituir função getRandomIcon() pela função real que usa o parâmetro da API**
    *   [x] Criar novo composable para buscar tipos de importação
    *   [x] Integrar com novo endpoint
    *   [x] Atualizar tipagem conforme contrato da API

---

## Task: SO-713 - Nova Aba "Log de Atualizações" no SponsorCockpitDetail

### Implementação da Nova Aba

1.  [x] Configuração das Abas:
    *   [x] Atualizar `detailsTabs.ts` para incluir a nova aba:
        *   [x] Manter array `detailsTabs` com as abas básicas
        *   [x] Criar array `detailsTabsWithLog` que inclui todas as abas básicas + aba de log

2.  [x] Controle de Permissão:
    *   [x] Reutilizar o composable `useSponsorCockpitPermissions` para verificar permissão de edição
    *   [x] Criar computed property `availableTabs` que retorna o array de abas apropriado baseado na permissão
    *   [x] A aba só aparece se a role `meusclientes.originacoes_sacados` for igual a 2 (mesmo critério do botão reprocessar)

3.  [x] Atualização do Componente SponsorCockpitDetail:
    *   [x] Importar ambos os arrays de abas (`detailsTabs` e `detailsTabsWithLog`)
    *   [x] Usar `availableTabs` no componente `farm-tabs`
    *   [x] Adicionar placeholder para o conteúdo da aba de log
    *   [x] Ajustar lógica de navegação e índices para usar `availableTabs`

4.  [ ] Implementação do Conteúdo:
    *   [ ] Criar componente para o conteúdo da aba de log (será implementado posteriormente)
    *   [ ] Integrar com API de logs (será implementado posteriormente)

---

## Task: SO-715 - Criação e Integração do Modal de Reprocessamento

### Implementação do Modal (UI e Lógica Inicial)

*   [ ] **Estrutura do Modal:**
    *   [x] Criar componente Vue para o modal (ex: `ReprocessModal.vue`) em `src/features/SponsorCockpit/components/`.
    *   [x] Adicionar <farm-modal> como base no novo componente.
    *   [x] Importar e registrar `ReprocessModal.vue` em `SponsorCockpitList.vue`.
    *   [x] Adicionar <ReprocessModal> ao template de `SponsorCockpitList.vue`.
    *   [x] Implementar lógica de exibição/ocultação no `SponsorCockpitList.vue` (controlada por `isReprocessModalOpen`, `v-model` no modal).
    *   [x] Adicionar estado inicial e reset ao abrir o modal (atualizar `onReprocess` function em `SponsorCockpitList.vue`).
*   [ ] **Seção de Filtros no Modal:**
    *   [x] Adicionar `farm-textfield` para busca textual (`reprocessFilters.search`).
    *   [x] Adicionar `farm-select` para 'Status da Operação' (`reprocessFilters.status`) com opções mockadas (`mockReprocessStatus`).
    *   [x] Adicionar `farm-select` para 'Tipo de Importação' (`reprocessFilters.importType`) com opções mockadas (reutilizar `importTypes` de `SponsorCockpitList.vue`).
    *   [x] Implementar botões 'Aplicar Filtros' e 'Limpar Filtros' para o modal.
*   [ ] **Tabela de Dados no Modal:**
    *   [x] Adicionar `v-data-table` ou similar dentro do modal.
    *   [x] Definir e usar cabeçalhos (`mockReprocessTableHeaders`): Checkbox, Nome Sacado, Documento, Id Operação, Status Operação, Vencimento, Total Pago, Total Operação.
    *   [x] Implementar coluna de Checkbox (`v-model="selectedReprocessItems"`) para seleção múltipla.
    *   [x] Reutilizar lógica de ícone para 'Id da Operação' (ex: `getRandomIcon` ou similar).
    *   [x] Criar/Utilizar componente de *chip* para 'Status da Operação' (baseado em `mockReprocessStatus`).
    *   [x] Configurar colunas ordenáveis (Vencimento, Total Pago, Total Operação).
    *   [x] Popular tabela com dados mockados (`mockReprocessTableData`).
    *   [x] Implementar lógica de seleção usando composable `useTableSelection`.
    *   [x] Adicionar estados visuais para seleção total/parcial.
*   [ ] **Paginação da Tabela no Modal:**
    *   [ ] Adicionar <farm-datatable-paginator> ao modal.
    *   [ ] Implementar lógica de paginação (`reprocessPage`, `reprocessPageSize`, `reprocessTotalPages`, `onChangeReprocessPage`, `onChangeReprocessPageLimit`). (Inicialmente client-side com mock data).
*   [ ] **Rodapé do Modal (Footer):**
    *   [x] Adicionar slot `#footer` no <farm-modal>.
    *   [x] Adicionar botão 'Cancelar' (`@click="closeReprocessModal"`).
    *   [x] Adicionar botão 'Reprocessar' (`@click="handleReprocessConfirm"`).
    *   [ ] Habilitar botão 'Reprocessar' somente quando `selectedReprocessItems.length > 0`.

### Integração Backend (Posterior)

*   [ ] Criar novo composable para a funcionalidade de reprocessamento (ex: `useSponsorCockpitReprocess`).
*   [ ] Integrar com novo endpoint (aguardando definição).
*   [ ] Adicionar feedback visual durante o processamento (loading state no modal/botão).
*   [ ] Implementar tratamento de erros da API.
*   [ ] Substituir dados mockados (filtros, tabela) por chamadas de API no composable/modal.
*   [ ] Implementar lógica real de reprocessamento em `handleReprocessConfirm` chamando o composable.
*   [ ] Atualizar testes relacionados à nova funcionalidade.
