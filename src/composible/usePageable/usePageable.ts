import { ref, computed, watch, nextTick } from 'vue';

import { Ref } from 'vue/types/v3-generated';

export interface VuetifyTableSort {
	field: string;
	descending: 'ASC' | 'DESC';
}

interface UsePageable {
	page: number | Ref<number>;
	pagination: any;
	isFilterCounter: Ref<boolean>;
	isOpenFilter: boolean | Ref<boolean>;
	filterCurrent: Ref<any>;
	onChangePageLimit: Function;
	onChangePage: Function;
	onSortTable: Function;
	onSortSelect: Function;
	onClickMainFilter: Function;
	onInputChangeMainFilter: (value: string) => void;
	onApplyFilter: Function;
	onFiltersApplied: Function;
}

interface Sort {
	order: string;
	orderBy: string;
}

interface LowerCasedSort {
	order: string;
	orderby: string;
}

interface PropsType {
	calbackFn: (filters: any) => void;
	keyInputSearch: string;
	filters?: any;
	lowercaseSort?: boolean;
	sort?: Sort | LowerCasedSort;
	charInputSearch?: number;
	lazyFilters?: boolean;
}

export interface Pagination {
	pageNumber: number;
	pageSize: number;
	sort: null;
	totalElements: number;
	totalPages: number;
}

export function usePageable(props: PropsType, paginationModel: Pagination): UsePageable {
	const canDoSearch = ref(true);
	const lazyFilters = ref(props.lazyFilters || false);
	const pagination = ref(paginationModel);
	const filterCurrent = ref(props.filters || null);
	const sortCurrent = ref(props.sort || null);
	const isOpenFilter = ref(false);
	const isFilterCounter = ref(false);
	const lowercaseSort = ref(props.lowercaseSort || false);
	const key = ref(props.keyInputSearch || 'search');
	const quantChar = ref(props.charInputSearch || 3);
	const pageTriggerFromInput = ref(false);

	const page = computed(() => (pagination.value?.pageNumber || 0) + 1);

	function validParams(value = '') {
		const hasMainFilter = !!value;
		const hasSortValues =
			sortCurrent.value && Object.values(sortCurrent.value).filter(Boolean).length;
		const hasFilterValues =
			filterCurrent.value && Object.values(filterCurrent.value).filter(Boolean).length;

		return {
			page: pagination.value.pageNumber,
			limit: pagination.value.pageSize,
			...(hasFilterValues ? filterCurrent.value : {}),
			...(hasSortValues ? sortCurrent.value : {}),
			...(hasMainFilter ? { [key.value]: value } : {}),
		};
	}

	function onChangePageLimit(newPageLimit: number): void {
		pagination.value.pageSize = newPageLimit;
		pagination.value.pageNumber = 0;
		filterCurrent.value = {
			...filterCurrent.value,
			page: 0,
			limit: newPageLimit
		};

		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onChangePage(newPage: number): void {
		if (pageTriggerFromInput.value) {
			pageTriggerFromInput.value = false;
			return;
		}

		pagination.value.pageNumber = newPage - 1;

		filterCurrent.value = {
			...filterCurrent.value,
			page: (newPage - 1),
		};

		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function getOrderByKey(): string {
		return lowercaseSort.value ? 'orderby' : 'orderBy';
	}

	function onSortTable(tableSort: VuetifyTableSort): void {
		if (!tableSort) {
			return;
		}

		sortCurrent.value[getOrderByKey()] = tableSort.field;
		sortCurrent.value.order = tableSort.descending;
		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onSortSelect(value: string): void {
		const [orderby, order] = value.split('_');
		sortCurrent.value[getOrderByKey()] = orderby;
		sortCurrent.value.order = order;
		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onClickMainFilter(): void {
		isOpenFilter.value = !isOpenFilter.value;
	}

	function onApplyFilter(data): void {
		filterCurrent.value = {
			...filterCurrent.value,  // Preserve existing filters including search terms
			...data,
		};
		pagination.value.pageNumber = 0;

		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onFiltersApplied(data): void {
		isFilterCounter.value = data;
	}

	function onInputChangeMainFilter(value: string): void {
		if (value.length >= quantChar.value || value === '') {
			pageTriggerFromInput.value = true;
			pagination.value.pageNumber = 0;

			if (canDoSearch) {
				props.calbackFn(validParams(value));

				nextTick(() => {
					pageTriggerFromInput.value = false;
				});
			}
		}
	}

	watch(props.filters, () => {
		pagination.value.pageNumber = 0;
		if (lazyFilters.value) return;
		props.calbackFn(validParams());
	});

	return {
		page,
		pagination,
		isOpenFilter,
		isFilterCounter,
		filterCurrent,
		onChangePageLimit,
		onChangePage,
		onSortTable,
		onSortSelect,
		onClickMainFilter,
		onInputChangeMainFilter,
		onApplyFilter,
		onFiltersApplied,
	};
}
