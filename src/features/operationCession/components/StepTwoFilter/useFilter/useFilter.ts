import { reactive } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';

import { filterValues } from '../configurations';

type FilterTypes = {
	state: {
		items: {
			value: string | null;
		};
		expiredAtRange: string[];
		emissionDate: string[];
		filters: {
			value: string | null;
			expirationDateStart: string | null;
			expirationDateEnd: string | null;
			emissionDateRange: string | null;
		};
	};
	onFilterConfirm: Function;
	onFilterClear: Function;
	onInputRangedatepickerExpirationDate: Function;
	onInputRangedatepickerEmissionDate: Function;
};

export function useFilter(emit): FilterTypes {
	const { trigger } = useAnalytics();
	const state = reactive({
		items: filterValues,
		expiredAtRange: [],
		emissionDateRange: [],

		filters: {
			value: null,
			expirationDateStart: null,
			expirationDateEnd: null,
			emissionDateStart: null,
			emissionDateEnd: null,
		},
	});

	function onInputRangedatepickerEmissionDate(value: string): void {
		if (value.length === 2) {
			const startDate = new Date(value[0]);
			const endDate = new Date(value[1]);
			if (startDate > endDate) {
				state.emissionDateRange = [value[1], value[0]];
				return;
			}
		}
		state.emissionDateRange = value;
	}

	function onInputRangedatepickerExpirationDate(value: string): void {
		if (value.length === 2) {
			const startDate = new Date(value[0]);
			const endDate = new Date(value[1]);
			if (startDate > endDate) {
				state.expiredAtRange = [value[1], value[0]];
				return;
			}
		}
		state.expiredAtRange = value;
	}

	function onFilterConfirm(): void {
		if (state.expiredAtRange.length > 0) {
			state.filters.expirationDateStart = state.expiredAtRange[0];
			state.filters.expirationDateEnd = state.expiredAtRange[1];
		}

		if (state.emissionDateRange.length > 0) {
			state.filters.emissionDateStart = state.emissionDateRange[0];
			state.filters.emissionDateEnd = state.emissionDateRange[1];
		}
		const dataTrigger = {
			event: 'operation_cession',
			payload: {
				step: 2,
				description: 'clicou na opção de aplicar filtro',
				filter: {
					value: state.filters.value || 'N/A',
					expirationDateStart: state.filters.expirationDateStart || 'N/A',
					expirationDateEnd: state.filters.expirationDateEnd || 'N/A',
					emissionDateStart: state.filters.emissionDateStart || 'N/A',
					emissionDateEnd: state.filters.emissionDateEnd || 'N/A',
				},
			},
		};
		trigger(dataTrigger);
		emit('onApply', { ...state.filters });
		emit('onFiltersApplied', true);
	}

	function onFilterClear(): void {
		state.filters = {
			value: null,
			expirationDateStart: null,
			expirationDateEnd: null,
			emissionDateStart: null,
			emissionDateEnd: null,
		};
		state.expiredAtRange = [];
		state.emissionDateRange = [];
		const dataTrigger = {
			event: 'operation_cession',
			payload: {
				step: 2,
				description: 'clicou na opção de limpar filtro',
			},
		};
		trigger(dataTrigger);
		emit('onApply', {});
		emit('onFiltersApplied', false);
	}

	return {
		state,
		onFilterConfirm,
		onFilterClear,
		onInputRangedatepickerExpirationDate,
		onInputRangedatepickerEmissionDate,
	};
}
