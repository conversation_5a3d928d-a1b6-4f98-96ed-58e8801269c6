// Test script to verify the usePageable fix
// This simulates the bug scenario and verifies the fix

console.log('🧪 Testing usePageable fix...\n');

// Simulate the old broken behavior
function oldOnApplyFilter(filterCurrent, data) {
    return {
        ...data, // ❌ This overwrites everything, losing search terms
    };
}

// Simulate the new fixed behavior
function newOnApplyFilter(filterCurrent, data) {
    return {
        ...filterCurrent, // ✅ This preserves existing filters including search terms
        ...data,
    };
}

// Test scenario: User searches, then applies filters
const initialState = {};
const afterSearch = { search: 'operacao123' };
const filterData = { status: 'EM_ANALISE', limit: 10 };

console.log('📋 Test Scenario:');
console.log('1. Initial state:', initialState);
console.log('2. After search:', afterSearch);
console.log('3. Filter data to apply:', filterData);
console.log('');

// Test old behavior (broken)
const oldResult = oldOnApplyFilter(afterSearch, filterData);
console.log('❌ OLD BEHAVIOR (broken):');
console.log('   Result:', oldResult);
console.log('   Search term preserved?', oldResult.search ? '✅ YES' : '❌ NO');
console.log('');

// Test new behavior (fixed)
const newResult = newOnApplyFilter(afterSearch, filterData);
console.log('✅ NEW BEHAVIOR (fixed):');
console.log('   Result:', newResult);
console.log('   Search term preserved?', newResult.search ? '✅ YES' : '❌ NO');
console.log('');

// Verify the fix works
const isFixed = newResult.search === 'operacao123' && 
               newResult.status === 'EM_ANALISE' && 
               newResult.limit === 10;

console.log('🎯 FIX VERIFICATION:');
console.log('   Fix successful?', isFixed ? '✅ YES' : '❌ NO');

if (isFixed) {
    console.log('   ✅ Search terms are preserved when filters are applied');
    console.log('   ✅ New filter values are correctly applied');
    console.log('   ✅ No data loss occurs');
} else {
    console.log('   ❌ Fix failed - search terms are still lost');
}
